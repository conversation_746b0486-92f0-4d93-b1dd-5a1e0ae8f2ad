syntax = "proto3"; // نحدد أننا نستخدم Protobuf 3

option java_package = "com.example.grpc.helloworld"; // حزمة Java التي سيتم توليد الكود فيها
option java_multiple_files = true; // لإنشاء ملفات Java منفصلة لكل رسالة وخدمة

// الخدمة التي سنقوم بإنشائها
service Greeter {
  // Unary RPC: ترسل طلبًا واحدًا وتستقبل استجابة واحدة
  rpc SayHello (HelloRequest) returns (HelloResponse) {}
}

// تعريف الرسائل
message HelloRequest {
  string name = 1; // حقل اسم من نوع string وله رقم 1 (فريد داخل الرسالة)
}

message HelloResponse {
  string message = 1; // حقل رسالة من نوع string وله رقم 1
}