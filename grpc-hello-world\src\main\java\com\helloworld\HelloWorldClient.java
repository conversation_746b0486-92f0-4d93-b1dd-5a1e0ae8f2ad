package com.helloworld;

// import com.example.grpc.helloworld.GreeterGrpc;
// import com.example.grpc.helloworld.HelloRequest;
// import com.example.grpc.helloworld.HelloResponse;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.StatusRuntimeException;

import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;

public class HelloWorldClient {
    private static final Logger logger = Logger.getLogger(HelloWorldClient.class.getName());

    private final ManagedChannel channel; // القناة التي تربط العميل بالخادم
    private final GreeterGrpc.GreeterBlockingStub blockingStub; // Stub متزامن (Blocking) للخدمة

    /**
     * إنشاء عميل جديد يتصل بالخادم على المضيف والمنفذ المحددين.
     */
    public HelloWorldClient(String host, int port) {
        // بناء القناة
        this.channel = ManagedChannelBuilder.forAddress(host, port)
                .usePlaintext() // لتجاهل TLS/SSL في هذا المثال البسيط
                .build();
        // بناء Stub متزامن للخدمة من القناة
        blockingStub = GreeterGrpc.newBlockingStub(channel);
    }

    // إغلاق القناة بشكل صحيح
    public void shutdown() throws InterruptedException {
        channel.shutdown().awaitTermination(5, TimeUnit.SECONDS);
    }

    /**
     * إرسال طلب SayHello إلى الخادم.
     */
    public void greet(String name) {
        logger.info("Will try to greet " + name + "...");
        // بناء طلب HelloRequest
        HelloRequest request = HelloRequest.newBuilder().setName(name).build();
        HelloResponse response;
        try {
            // استدعاء دالة SayHello على الـ stub
            response = blockingStub.SayHello(request);
        } catch (StatusRuntimeException e) {
            logger.log(Level.WARNING, "RPC failed: {0}", e.getStatus());
            return;
        }
        // طباعة الاستجابة المستلمة من الخادم
        logger.info("Greeting: " + response.getMessage());
    }

    public static void main(String[] args) throws Exception {
        // تشغيل العميل للاتصال بالخادم على localhost:50051
        HelloWorldClient client = new HelloWorldClient("localhost", 50051);
        try {
            String user = "world";
            if (args.length > 0) {
                user = args[0]; // السماح بتمرير الاسم كمعامل
            }
            client.greet(user); // استدعاء دالة الترحيب
        } finally {
            client.shutdown(); // تأكد من إغلاق القناة عند الانتهاء
        }
    }
}mvn exec:java -Dexec.mainClass="com.example.grpc.helloworld.HelloWorldServer"