package com.helloworld;

// import com.example.grpc.helloworld.GreeterGrpc;
// import com.example.grpc.helloworld.HelloRequest;
// import com.example.grpc.helloworld.HelloResponse;
import io.grpc.Server;
import io.grpc.ServerBuilder;
import io.grpc.stub.StreamObserver;

import java.io.IOException;
import java.util.logging.Logger;

public class HelloWorldServer {
    private static final Logger logger = Logger.getLogger(HelloWorldServer.class.getName());

    private Server server; // كائن الخادم gRPC

    // تشغيل الخادم
    private void start() throws IOException {
        int port = 50051; // المنفذ الذي سيستمع عليه الخادم
        server = ServerBuilder.forPort(port)
                .addService(new GreeterImpl()) // إضافة تنفيذ خدمتنا
                .build()
                .start();
        logger.info("Server started, listening on " + port);

        // إضافة خطاف إغلاق لإيقاف الخادم عند إغلاق JVM
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            System.err.println("*** shutting down gRPC server since JVM is shutting down");
            try {
                HelloWorldServer.this.stop();
            } catch (InterruptedException e) {
                e.printStackTrace(System.err);
            }
            System.err.println("*** server shut down");
        }));
    }

    // إيقاف الخادم
    private void stop() throws InterruptedException {
        if (server != null) {
            server.shutdown().awaitTermination();
        }
    }

    // حظر الخادم حتى يتم إغلاقه
    private void blockUntilShutdown() throws InterruptedException {
        if (server != null) {
            server.awaitTermination();
        }
    }

    // الدالة الرئيسية لتشغيل الخادم
    public static void main(String[] args) throws IOException, InterruptedException {
        final HelloWorldServer server = new HelloWorldServer();
        server.start();
        server.blockUntilShutdown();
    }

    // الفئة الداخلية التي تنفذ خدمة Greeter
    static class GreeterImpl extends GreeterGrpc.GreeterImplBase {
        // تنفيذ دالة SayHello
        @Override
        public void SayHello(HelloRequest request, StreamObserver<HelloResponse> responseObserver) {
            // طباعة الاسم الذي تم استلامه من العميل
            logger.info("Received request from: " + request.getName());

            // بناء الاستجابة
            HelloResponse response = HelloResponse.newBuilder()
                    .setMessage("Hello " + request.getName())
                    .build();

            // إرسال الاستجابة مرة أخرى إلى العميل
            responseObserver.onNext(response);

            // إعلام العميل بأن الاستجابة قد اكتملت
            responseObserver.onCompleted();
        }
    }
}